\subsection{Hand Detection and Tracking}

The foundation of our gesture recognition system relies on MediaPipe Hands~\cite{zhang2020mediapipe}, which provides robust real-time hand detection and precise landmark tracking capabilities. This framework delivers 21 distinct hand landmarks with complete 3D coordinate information, establishing the basis for comprehensive gesture analysis.

Our hand tracking implementation incorporates several critical enhancements to optimize gesture recognition performance and maintain operational stability. The system accommodates simultaneous detection of both hands, effectively doubling the potential gesture vocabulary and enabling more sophisticated interaction patterns. Reliability is ensured through carefully calibrated confidence thresholds: detection confidence is maintained at 0.7 while tracking confidence operates at 0.5, striking an optimal balance between responsiveness and accuracy. Additionally, temporal smoothing algorithms are applied to landmark trajectories, significantly reducing noise artifacts and delivering consistent tracking performance across frame sequences.

\subsubsection{Feature Engineering}

Our approach to gesture classification relies on extracting meaningful characteristics from the 21 3D hand landmarks provided by MediaPipe. The feature extraction methodology encompasses both spatial relationships and temporal dynamics to create comprehensive gesture representations.

\begin{itemize}
    \item \textbf{Geometric descriptors}: These encompass inter-landmark distances, joint angles, and directional vectors that characterize the hand's structural configuration and spatial orientation.

    For any landmark pair $i$ and $j$, the Euclidean distance is calculated as:
    \[
    d_{ij} = \sqrt{(x_i - x_j)^2 + (y_i - y_j)^2 + (z_i - z_j)^2}
    \]
    where $(x_i, y_i, z_i)$ and $(x_j, y_j, z_j)$ represent the respective 3D coordinates.

    \item \textbf{Finger configuration indicators}: Binary classification features determine the extension or flexion state of each finger, enabling discrimination between gestures with similar overall hand shapes but different finger positions.

    \item \textbf{Scale-invariant coordinates}: Landmark positions are normalized with respect to wrist location and overall hand dimensions, ensuring robust performance across users with diverse anatomical characteristics.

    \item \textbf{Motion characteristics}: Velocity and acceleration vectors computed across consecutive frames capture the dynamic aspects of gesture execution.

    The instantaneous velocity of landmark $i$ at frame $t$ is determined by:
    \[
    v_i^{(t)} = \frac{p_i^{(t)} - p_i^{(t-1)}}{\Delta t}
    \]
    where $p_i^{(t)}$ denotes the landmark position at frame $t$ and $\Delta t$ represents the inter-frame time interval.
\end{itemize}

This multi-dimensional feature representation effectively captures both the static configuration and dynamic behavior of hand gestures, providing the neural network with rich input data for accurate classification decisions.
